import os
from dotenv import load_dotenv
from sqlalchemy import create_engine
from langchain_community.utilities import SQLDatabase

def load_env():
    from pathlib import Path
    # 尝试多个可能的.env文件位置
    possible_paths = [
        Path(__file__).parent.parent.parent / '.env',  # 项目根目录
        Path(__file__).parent.parent / '.env',         # app目录的父目录
        Path('.env'),                                   # 当前工作目录
    ]

    for env_path in possible_paths:
        if env_path.exists():
            load_dotenv(dotenv_path=env_path, verbose=False)
            return

    load_dotenv(verbose=False)

load_env()

# 从环境变量中获取数据库连接URL
DB_URL = os.getenv("DB_URL")
if not DB_URL:
    raise ValueError("环境变量 DB_URL 未设置，请检查 .env 文件。")

# 创建 SQLAlchemy 引擎，全局复用
engine = create_engine(DB_URL, future=True, echo=False)

# 通过 engine.url 提取连接属性
url = engine.url
host = url.host
port = url.port
user = url.username
password = url.password
database = url.database
# 创建 LangChain 的 SQLDatabase 实例
db = SQLDatabase(engine=engine)

# 将连接属性挂在 db 对象上，方便直接使用
setattr(db, 'host', host)
setattr(db, 'port', port)
setattr(db, 'user', user)
setattr(db, 'password', password)
setattr(db, 'database', database)
