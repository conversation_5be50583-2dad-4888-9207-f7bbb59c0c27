#!/usr/bin/env python3
"""
FastAPI迁移验证测试脚本

验证Flask到FastAPI迁移后所有API端点的兼容性和功能正确性。
确保外部客户端无需任何修改即可正常工作。
"""

import sys
import os
import asyncio
import json
import httpx
import pytest
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi.testclient import TestClient
from app import create_app

# 创建测试客户端
app = create_app()
client = TestClient(app)


class TestAPICompatibility:
    """API兼容性测试类"""

    def test_suggestions_endpoint_structure(self):
        """测试建议接口的请求/响应结构兼容性"""
        # 测试正常请求
        response = client.post("/api/suggestions", json={"query": "按学历"})
        assert response.status_code == 200
        
        data = response.json()
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)
        assert len(data["suggestions"]) <= 5

    def test_suggestions_endpoint_validation(self):
        """测试建议接口的输入验证"""
        # 测试缺少query字段
        response = client.post("/api/suggestions", json={})
        assert response.status_code == 422  # FastAPI使用422而不是400进行验证错误
        
        # 测试空请求体
        response = client.post("/api/suggestions", json=None)
        assert response.status_code == 422

    def test_suggestions_functionality(self):
        """测试建议功能的正确性"""
        test_cases = [
            {"query": "按学历", "expected_contains": "学历"},
            {"query": "专家", "expected_contains": "专家"},
            {"query": "统计", "expected_contains": "统计"},
            {"query": "不存在的查询", "expected_empty": True}
        ]
        
        for case in test_cases:
            response = client.post("/api/suggestions", json={"query": case["query"]})
            assert response.status_code == 200
            
            data = response.json()
            suggestions = data["suggestions"]
            
            if case.get("expected_empty"):
                assert len(suggestions) == 0
            elif "expected_contains" in case:
                # 至少有一个建议包含期望的关键词
                found = any(case["expected_contains"] in suggestion for suggestion in suggestions)
                assert found, f"没有找到包含'{case['expected_contains']}'的建议"

    def test_query_endpoint_structure(self):
        """测试查询接口的基本结构"""
        # 注意：这里只测试接口结构，不测试实际的LLM调用
        response = client.post("/api/query", json={"question": "测试问题"})
        
        # 应该返回流式响应
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream; charset=utf-8"

    def test_query_endpoint_validation(self):
        """测试查询接口的输入验证"""
        # 测试缺少question字段
        response = client.post("/api/query", json={})
        assert response.status_code == 422
        
        # 测试空请求体
        response = client.post("/api/query", json=None)
        assert response.status_code == 422

    def test_cors_headers(self):
        """测试CORS配置是否正确"""
        # 测试预检请求
        response = client.options("/api/suggestions")
        assert response.status_code == 200
        
        # 测试实际请求的CORS头
        response = client.post("/api/suggestions", json={"query": "test"})
        # FastAPI的CORS中间件会自动添加必要的头部

    def test_api_documentation(self):
        """测试API文档是否可访问"""
        # 测试Swagger UI
        response = client.get("/docs")
        assert response.status_code == 200
        
        # 测试ReDoc
        response = client.get("/redoc")
        assert response.status_code == 200
        
        # 测试OpenAPI规范
        response = client.get("/openapi.json")
        assert response.status_code == 200
        
        openapi_spec = response.json()
        assert "paths" in openapi_spec
        assert "/api/suggestions" in openapi_spec["paths"]
        assert "/api/query" in openapi_spec["paths"]


class TestStreamingCompatibility:
    """流式响应兼容性测试"""

    def test_streaming_response_format(self):
        """测试流式响应格式是否与原Flask版本兼容"""
        # 这个测试需要模拟LLM服务，或者使用真实的API密钥
        # 为了避免依赖外部服务，这里只测试基本的流式响应结构
        
        with client.stream("POST", "/api/query", json={"question": "简单测试问题"}) as response:
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
            
            # 检查是否有数据流
            content_received = False
            for chunk in response.iter_text():
                if chunk.strip():
                    content_received = True
                    # 验证SSE格式
                    assert "event:" in chunk or "data:" in chunk
                    break
            
            # 注意：如果没有配置有效的LLM API，这里可能会收到错误事件
            # 这是正常的，重要的是响应格式正确


def run_compatibility_tests():
    """运行所有兼容性测试"""
    print("=== FastAPI迁移兼容性测试 ===")
    
    # 运行pytest
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_compatibility_tests()
