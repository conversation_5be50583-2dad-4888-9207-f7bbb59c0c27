from fastapi import FastAPI
from .middleware import setup_middleware
from .routes import api_router
import logging

logger = logging.getLogger(__name__)

def create_app() -> FastAPI:
    """FastAPI应用工厂函数。"""
    app = FastAPI(
        title="ChatBI API",
        description="智能数据分析平台API",
        version="1.0.0",
        docs_url="/docs",  # Swagger UI
        redoc_url="/redoc"  # ReDoc
    )

    # 设置中间件（包括CORS）
    setup_middleware(app)

    # 注册API路由
    app.include_router(api_router, prefix="/api")

    logger.info("FastAPI应用创建完成")
    return app