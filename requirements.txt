 aiohappyeyeballs==2.6.1
aiohttp==3.12.11
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
async-timeout==4.0.3
attrs==25.3.0
blinker==1.9.0
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
cors==1.0.1
dashscope==1.23.4
dataclasses-json==0.6.7
distro==1.9.0
exceptiongroup==1.3.0
fastapi==0.115.12
filelock==3.18.0
# Flask packages removed for FastAPI migration
# Flask==3.1.1
# flask-cors==6.0.0
frozenlist==1.6.2
fsspec==2025.5.1
future==1.0.0
gevent==25.5.1
greenlet==3.2.3
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.32.4
idna==3.10
itsdangerous==2.2.0
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.64
langchain-huggingface==0.2.0
langchain-openai==0.3.21
langchain-text-splitters==0.3.8
langsmith==0.3.45
MarkupSafe==3.0.2
marshmallow==3.26.1
mpmath==1.3.0
multidict==6.4.4
mypy_extensions==1.1.0
mysql-connector-python==9.3.0
networkx==3.4.2
numpy==2.2.6
openai==1.84.0
orjson==3.10.18
packaging==24.2
pandas==2.3.0
pillow==11.2.1
propcache==0.3.1
pycparser==2.22
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
PyMySQL==1.1.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
pytz==2025.2
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-toolbelt==1.0.0
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.15.3
sentence-transformers==4.1.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
starlette==0.46.2
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tldextract==5.3.0
tokenizers==0.21.1
torch==2.7.1
tqdm==4.67.1
transformers==4.52.4
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
urllib3==2.4.0
utils==1.0.2
uvicorn==0.34.3
watchfiles==1.0.5
websocket-client==1.8.0
websockets==15.0.1
# Werkzeug removed for FastAPI migration
# Werkzeug==3.1.3
yarl==1.20.0
zope.event==5.0
zope.interface==7.2
zstandard==0.23.0