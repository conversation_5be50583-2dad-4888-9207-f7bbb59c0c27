import logging
from app import create_app

# ----------------------
# 日志配置：只显示关键信息
# ----------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s: %(message)s"
)

# 设置各个模块的日志级别
logging.getLogger("werkzeug").setLevel(logging.WARNING)  # Flask请求日志
logging.getLogger("openai").setLevel(logging.WARNING)    # OpenAI客户端日志
logging.getLogger("httpx").setLevel(logging.WARNING)     # HTTP请求日志
logging.getLogger("httpcore").setLevel(logging.WARNING)  # HTTP核心日志
logging.getLogger("langchain_core").setLevel(logging.WARNING)  # LangChain核心日志
logging.getLogger("asyncio").setLevel(logging.WARNING)   # 异步IO日志

# ----------------------
# Flask 应用启动
# ----------------------
app = create_app()

if __name__ == '__main__':
    # 在调试模式下启动，控制台将输出 LCEL 工作流的详细日志
    app.run(host='0.0.0.0', port=5000, debug=True)