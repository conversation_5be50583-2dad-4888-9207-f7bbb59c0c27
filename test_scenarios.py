#!/usr/bin/env python3
"""
压力测试场景定义

定义各种测试场景，包括真实业务查询集合、不同复杂度的查询模板、
用户行为模式模拟和负载分布策略。
"""

import random
import json
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum


class QueryComplexity(Enum):
    """查询复杂度枚举"""
    SIMPLE = "simple"      # 简单查询，预估100-200 tokens
    MEDIUM = "medium"      # 中等查询，预估300-500 tokens
    COMPLEX = "complex"    # 复杂查询，预估600-1000 tokens


@dataclass
class TestQuery:
    """测试查询定义"""
    question: str
    complexity: QueryComplexity
    estimated_tokens: int
    category: str
    description: str


@dataclass
class UserBehaviorPattern:
    """用户行为模式"""
    name: str
    query_distribution: Dict[QueryComplexity, float]  # 查询复杂度分布
    think_time_range: Tuple[float, float]  # 思考时间范围（秒）
    session_duration_range: Tuple[int, int]  # 会话持续时间范围（查询次数）
    concurrent_probability: float  # 并发查询概率


class TestScenarios:
    """测试场景管理器"""
    
    def __init__(self):
        self.queries = self._initialize_queries()
        self.user_patterns = self._initialize_user_patterns()
        self.suggestion_queries = self._initialize_suggestion_queries()
    
    def _initialize_queries(self) -> List[TestQuery]:
        """初始化测试查询集合"""
        return [
            # 简单查询 (100-200 tokens)
            TestQuery(
                question="统计专家总数",
                complexity=QueryComplexity.SIMPLE,
                estimated_tokens=120,
                category="基础统计",
                description="简单的计数查询"
            ),
            TestQuery(
                question="按性别统计专家数量",
                complexity=QueryComplexity.SIMPLE,
                estimated_tokens=150,
                category="分组统计",
                description="简单的分组计数"
            ),
            TestQuery(
                question="查询最近注册的10位专家",
                complexity=QueryComplexity.SIMPLE,
                estimated_tokens=180,
                category="排序查询",
                description="简单的排序和限制"
            ),
            TestQuery(
                question="统计各个学历层次的专家数量",
                complexity=QueryComplexity.SIMPLE,
                estimated_tokens=160,
                category="学历统计",
                description="按学历分组统计"
            ),
            TestQuery(
                question="查询专家的平均年龄",
                complexity=QueryComplexity.SIMPLE,
                estimated_tokens=140,
                category="聚合查询",
                description="简单的聚合函数"
            ),
            
            # 中等查询 (300-500 tokens)
            TestQuery(
                question="按学历和性别交叉统计专家分布情况",
                complexity=QueryComplexity.MEDIUM,
                estimated_tokens=350,
                category="交叉分析",
                description="多维度分组统计"
            ),
            TestQuery(
                question="统计各职业类型中博士学历专家的比例",
                complexity=QueryComplexity.MEDIUM,
                estimated_tokens=400,
                category="比例分析",
                description="条件过滤和比例计算"
            ),
            TestQuery(
                question="查询年龄在30-50岁之间的教授级专家信息",
                complexity=QueryComplexity.MEDIUM,
                estimated_tokens=380,
                category="条件查询",
                description="多条件过滤查询"
            ),
            TestQuery(
                question="分析不同地区专家的学历分布特点",
                complexity=QueryComplexity.MEDIUM,
                estimated_tokens=420,
                category="地域分析",
                description="地区和学历的关联分析"
            ),
            TestQuery(
                question="统计各个年龄段专家的职业分布情况",
                complexity=QueryComplexity.MEDIUM,
                estimated_tokens=360,
                category="年龄分析",
                description="年龄段和职业的关联"
            ),
            
            # 复杂查询 (600-1000 tokens)
            TestQuery(
                question="分析专家的学历、职业、年龄三个维度的关联关系，并给出详细的数据洞察",
                complexity=QueryComplexity.COMPLEX,
                estimated_tokens=750,
                category="多维分析",
                description="三维关联分析和洞察"
            ),
            TestQuery(
                question="对比分析男性和女性专家在不同职业领域的学历分布差异，并解释可能的原因",
                complexity=QueryComplexity.COMPLEX,
                estimated_tokens=850,
                category="对比分析",
                description="性别差异的深度分析"
            ),
            TestQuery(
                question="基于专家的地域分布、学历结构和职业类型，分析人才聚集的规律和特点",
                complexity=QueryComplexity.COMPLEX,
                estimated_tokens=900,
                category="规律分析",
                description="人才聚集规律的综合分析"
            ),
            TestQuery(
                question="通过多表关联查询，分析专家的申请记录、开发项目和收藏情况，找出最活跃的专家群体",
                complexity=QueryComplexity.COMPLEX,
                estimated_tokens=800,
                category="活跃度分析",
                description="多表关联的活跃度分析"
            ),
            TestQuery(
                question="综合分析专家的基本信息、项目参与度和学术背景，构建专家能力评估模型",
                complexity=QueryComplexity.COMPLEX,
                estimated_tokens=950,
                category="模型构建",
                description="专家能力评估的综合分析"
            )
        ]
    
    def _initialize_user_patterns(self) -> List[UserBehaviorPattern]:
        """初始化用户行为模式"""
        return [
            UserBehaviorPattern(
                name="casual_user",
                query_distribution={
                    QueryComplexity.SIMPLE: 0.7,
                    QueryComplexity.MEDIUM: 0.25,
                    QueryComplexity.COMPLEX: 0.05
                },
                think_time_range=(2.0, 8.0),
                session_duration_range=(1, 3),
                concurrent_probability=0.1
            ),
            UserBehaviorPattern(
                name="business_analyst",
                query_distribution={
                    QueryComplexity.SIMPLE: 0.3,
                    QueryComplexity.MEDIUM: 0.5,
                    QueryComplexity.COMPLEX: 0.2
                },
                think_time_range=(5.0, 15.0),
                session_duration_range=(3, 8),
                concurrent_probability=0.3
            ),
            UserBehaviorPattern(
                name="data_scientist",
                query_distribution={
                    QueryComplexity.SIMPLE: 0.2,
                    QueryComplexity.MEDIUM: 0.4,
                    QueryComplexity.COMPLEX: 0.4
                },
                think_time_range=(10.0, 30.0),
                session_duration_range=(5, 15),
                concurrent_probability=0.5
            ),
            UserBehaviorPattern(
                name="power_user",
                query_distribution={
                    QueryComplexity.SIMPLE: 0.1,
                    QueryComplexity.MEDIUM: 0.3,
                    QueryComplexity.COMPLEX: 0.6
                },
                think_time_range=(3.0, 10.0),
                session_duration_range=(8, 20),
                concurrent_probability=0.7
            )
        ]
    
    def _initialize_suggestion_queries(self) -> List[str]:
        """初始化建议查询的测试前缀"""
        return [
            "按学历",
            "统计专家",
            "查询博士",
            "分析年龄",
            "按性别",
            "教授级",
            "研究员",
            "地区分布",
            "职业类型",
            "学历分布",
            "年龄段",
            "专业领域",
            "工作经验",
            "学术背景",
            "项目经验"
        ]
    
    def get_queries_by_complexity(self, complexity: QueryComplexity) -> List[TestQuery]:
        """根据复杂度获取查询"""
        return [q for q in self.queries if q.complexity == complexity]
    
    def get_random_query(self, complexity: QueryComplexity = None) -> TestQuery:
        """获取随机查询"""
        if complexity:
            candidates = self.get_queries_by_complexity(complexity)
        else:
            candidates = self.queries
        
        return random.choice(candidates)
    
    def get_query_by_pattern(self, pattern: UserBehaviorPattern) -> TestQuery:
        """根据用户模式获取查询"""
        # 根据分布概率选择复杂度
        rand = random.random()
        cumulative = 0
        
        for complexity, probability in pattern.query_distribution.items():
            cumulative += probability
            if rand <= cumulative:
                return self.get_random_query(complexity)
        
        # 默认返回简单查询
        return self.get_random_query(QueryComplexity.SIMPLE)
    
    def get_random_suggestion_query(self) -> str:
        """获取随机建议查询前缀"""
        return random.choice(self.suggestion_queries)
    
    def generate_test_session(self, pattern: UserBehaviorPattern) -> List[TestQuery]:
        """生成测试会话"""
        session_length = random.randint(*pattern.session_duration_range)
        session_queries = []
        
        for _ in range(session_length):
            query = self.get_query_by_pattern(pattern)
            session_queries.append(query)
        
        return session_queries
    
    def estimate_session_tokens(self, session: List[TestQuery]) -> int:
        """估算会话的Token消耗"""
        return sum(query.estimated_tokens for query in session)
    
    def get_workload_distribution(self, total_users: int, 
                                pattern_weights: Dict[str, float] = None) -> Dict[str, int]:
        """获取工作负载分布"""
        if pattern_weights is None:
            # 默认分布
            pattern_weights = {
                "casual_user": 0.5,
                "business_analyst": 0.3,
                "data_scientist": 0.15,
                "power_user": 0.05
            }
        
        distribution = {}
        remaining_users = total_users
        
        pattern_names = list(pattern_weights.keys())
        for i, pattern_name in enumerate(pattern_names):
            if i == len(pattern_names) - 1:
                # 最后一个模式分配剩余用户
                distribution[pattern_name] = remaining_users
            else:
                count = int(total_users * pattern_weights[pattern_name])
                distribution[pattern_name] = count
                remaining_users -= count
        
        return distribution
    
    def create_mixed_workload(self, concurrent_users: int) -> List[Tuple[TestQuery, str]]:
        """创建混合工作负载"""
        workload = []
        distribution = self.get_workload_distribution(concurrent_users)
        
        for pattern_name, user_count in distribution.items():
            pattern = next(p for p in self.user_patterns if p.name == pattern_name)
            
            for _ in range(user_count):
                query = self.get_query_by_pattern(pattern)
                workload.append((query, pattern_name))
        
        # 随机打乱顺序
        random.shuffle(workload)
        return workload
    
    def get_pattern_by_name(self, name: str) -> UserBehaviorPattern:
        """根据名称获取用户模式"""
        return next((p for p in self.user_patterns if p.name == name), None)
    
    def calculate_expected_load(self, concurrent_users: int, 
                              duration_minutes: int) -> Dict[str, Any]:
        """计算预期负载"""
        distribution = self.get_workload_distribution(concurrent_users)
        
        total_queries = 0
        total_tokens = 0
        
        for pattern_name, user_count in distribution.items():
            pattern = self.get_pattern_by_name(pattern_name)
            
            # 估算每个用户的查询数量
            avg_session_length = sum(pattern.session_duration_range) / 2
            avg_think_time = sum(pattern.think_time_range) / 2
            
            # 估算在测试时间内的查询次数
            queries_per_user = (duration_minutes * 60) / (avg_think_time + 30)  # 假设平均响应时间30秒
            queries_per_user = min(queries_per_user, avg_session_length)  # 不超过会话长度
            
            user_queries = int(queries_per_user * user_count)
            total_queries += user_queries
            
            # 估算Token消耗
            avg_tokens_per_query = sum(
                complexity.value * prob * self._get_avg_tokens_for_complexity(complexity)
                for complexity, prob in pattern.query_distribution.items()
            )
            total_tokens += int(user_queries * avg_tokens_per_query)
        
        return {
            "total_queries": total_queries,
            "total_tokens": total_tokens,
            "estimated_qpm": total_queries / duration_minutes,
            "estimated_tpm": total_tokens / duration_minutes,
            "user_distribution": distribution
        }
    
    def _get_avg_tokens_for_complexity(self, complexity: QueryComplexity) -> int:
        """获取复杂度对应的平均Token数"""
        complexity_tokens = {
            QueryComplexity.SIMPLE: 150,
            QueryComplexity.MEDIUM: 400,
            QueryComplexity.COMPLEX: 800
        }
        return complexity_tokens.get(complexity, 150)
